"use client";
import { useState } from "react";
import {
  FaSearch,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaUsers,
  FaPlane,
  FaHotel,
  FaGlobe,
} from "react-icons/fa";
import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";

export default function AdvancedSearchBox({ className = "", style = {} }) {
  const [activeTab, setActiveTab] = useState("hotels-tunisie");
  const [searchData, setSearchData] = useState({
    destination: "",
    arrival: "",
    departure: "",
    rooms: 1,
    adults: 2,
    children: 0,
    category: "",
    availability: "all",
  });

  const searchTabs = [
    {
      id: "hotels-tunisie",
      label: "Hôtels en Tunisie",
      icon: <FaHotel className="w-4 h-4" />,
    },
    {
      id: "hotels-monde",
      label: "Hôtels à l'étranger",
      icon: <FaGlobe className="w-4 h-4" />,
    },
    {
      id: "voyages-organises",
      label: "Voyages organisés",
      icon: <FaMapMarkerAlt className="w-4 h-4" />,
    },
    {
      id: "vols",
      label: "Vols",
      icon: <FaPlane className="w-4 h-4" />,
    },
  ];

  const tunisianDestinations = [
    "Tunis", "Hammamet", "Sousse", "Monastir", "Mahdia", 
    "Tabarka", "Djerba", "Tozeur", "Douz", "Korba", "Nabeul"
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    console.log("Search data:", { type: activeTab, ...searchData });
    // Redirect to appropriate search results page
    const searchParams = new URLSearchParams({
      type: activeTab,
      ...searchData,
    });
    window.location.href = `/search?${searchParams.toString()}`;
  };

  return (
    <Card
      className={`relative rounded-3xl travel-card-shadow max-w-6xl w-[95%] md:w-4/5 lg:w-4/5 mx-auto ${className}`}
      style={style}
      padding="lg"
    >
      {/* Header */}
      <div className="text-center mb-6">
        <h1 className="text-heading text-2xl md:text-4xl text-accent mb-2">
          Réservez vos hôtels et vols en Tunisie et dans le monde
        </h1>
        <p className="text-body text-sm md:text-lg">
          à partir de 70 TND avec notre agence de voyage
        </p>
      </div>

      {/* Search Tabs */}
      <div className="flex flex-wrap justify-center gap-2 mb-6">
        {searchTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
              activeTab === tab.id
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            {tab.icon}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Destination */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-gray-700 text-sm font-semibold">
              <FaMapMarkerAlt className="text-accent" />
              Votre Destination
            </label>
            {activeTab === "hotels-tunisie" ? (
              <select
                name="destination"
                value={searchData.destination}
                onChange={handleInputChange}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Sélectionner une destination</option>
                {tunisianDestinations.map((dest) => (
                  <option key={dest} value={dest}>
                    {dest}
                  </option>
                ))}
              </select>
            ) : (
              <input
                type="text"
                name="destination"
                placeholder="Ajouter une destination"
                value={searchData.destination}
                onChange={handleInputChange}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            )}
          </div>

          {/* Arrival Date */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-gray-700 text-sm font-semibold">
              <FaCalendarAlt className="text-accent" />
              Votre Arrivée
            </label>
            <input
              type="date"
              name="arrival"
              value={searchData.arrival}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Departure Date */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-gray-700 text-sm font-semibold">
              <FaCalendarAlt className="text-accent" />
              Votre Départ
            </label>
            <input
              type="date"
              name="departure"
              value={searchData.departure}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Guests/Rooms */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-gray-700 text-sm font-semibold">
              <FaUsers className="text-accent" />
              Chambres et occupation
            </label>
            <div className="grid grid-cols-2 gap-2">
              <select
                name="adults"
                value={searchData.adults}
                onChange={handleInputChange}
                className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
              >
                {[1, 2, 3, 4, 5, 6].map((num) => (
                  <option key={num} value={num}>
                    {num} adulte{num > 1 ? "s" : ""}
                  </option>
                ))}
              </select>
              <select
                name="children"
                value={searchData.children}
                onChange={handleInputChange}
                className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
              >
                {[0, 1, 2, 3, 4].map((num) => (
                  <option key={num} value={num}>
                    {num} enfant{num > 1 ? "s" : ""}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Advanced Options */}
        {activeTab === "hotels-tunisie" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200">
            <div className="space-y-2">
              <label className="text-gray-700 text-sm font-semibold">Catégorie</label>
              <select
                name="category"
                value={searchData.category}
                onChange={handleInputChange}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Toutes catégories</option>
                <option value="5">5 étoiles</option>
                <option value="4">4 étoiles</option>
                <option value="3">3 étoiles</option>
              </select>
            </div>
            <div className="space-y-2">
              <label className="text-gray-700 text-sm font-semibold">Disponibilité</label>
              <select
                name="availability"
                value={searchData.availability}
                onChange={handleInputChange}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="all">Peu importe</option>
                <option value="available">Disponible uniquement</option>
              </select>
            </div>
          </div>
        )}

        {/* Search Button */}
        <div className="flex justify-center pt-4">
          <Button
            type="submit"
            variant="secondary"
            size="lg"
            className="px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
          >
            <FaSearch className="w-5 h-5 mr-2" />
            Rechercher
          </Button>
        </div>
      </form>
    </Card>
  );
}
